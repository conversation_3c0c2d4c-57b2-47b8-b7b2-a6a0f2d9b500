import React from 'react';
import styled from 'styled-components';
import Timer from './components/Timer';
import { AudioProvider } from './components/Audio';
import InstallPrompt from './components/InstallPrompt';

const AppContainer = styled.div`
  text-align: center;
  padding: 2rem;
`;

const Title = styled.h1`
  color: #333;
  font-size: 2rem;
  margin-bottom: 1rem;
`;

const App: React.FC = () => {
  return (
    <AppContainer>
      <Title>My Pomodoro Timer</Title>
      <p>Welcome to the Pomodoro Timer application!</p>
      <AudioProvider>
        <Timer />
      </AudioProvider>
      <InstallPrompt />
    </AppContainer>
  );
};

export default App;
