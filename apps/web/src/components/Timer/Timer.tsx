import React, { useState, useEffect, useRef } from 'react';
import {
  TimerContainer,
  TimeDisplay,
  TimerControls,
  SessionTypeIndicator,
} from './Timer.styles';
import { formatTime } from '../../utils/timeUtils';
import Button from '../common/Button';
import { useAudio } from '../Audio/AudioManager';
import { saveSession } from '../../utils/sessionStorageUtils';

// Default durations
const DEFAULT_WORK_DURATION = 1500; // 25 minutes
const DEFAULT_BREAK_DURATION = 300; // 5 minutes

// Session types
type SessionType = 'work' | 'break';

// Simple ID generator
const generateId = () => Math.random().toString(36).substr(2, 9);

const Timer: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState<number>(DEFAULT_WORK_DURATION);
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [sessionType, setSessionType] = useState<SessionType>('work');
  const { playSessionTransition } = useAudio();
  const animationFrameRef = useRef<number>(0);
  const lastTimestampRef = useRef<number>(0);

  // Format time for display
  const formattedTime = formatTime(timeLeft);

  // Timer logic using requestAnimationFrame
  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      const updateTimer = (timestamp: number) => {
        if (lastTimestampRef.current === 0) {
          lastTimestampRef.current = timestamp;
        }

        const elapsed = timestamp - lastTimestampRef.current;

        // Update every second
        if (elapsed >= 1000) {
          setTimeLeft(prev => {
            const newTime = prev - 1;
            if (newTime <= 0) {
              // Session completed - record it and transition to next session
              const newSessionType = sessionType === 'work' ? 'break' : 'work';
              const newDuration =
                newSessionType === 'work'
                  ? DEFAULT_WORK_DURATION
                  : DEFAULT_BREAK_DURATION;

              // Record completed session
              const completedSession = {
                id: generateId(),
                startTime: new Date(Date.now() - prev * 100), // Approximate start time
                endTime: new Date(),
                duration: prev,
                type: sessionType,
                completed: true,
              };

              saveSession(completedSession);

              // Play transition sound
              playSessionTransition();

              // Set new session type and duration
              setSessionType(newSessionType);
              setIsRunning(true); // Automatically start next session
              return newDuration;
            }
            return newTime;
          });
          lastTimestampRef.current = timestamp;
        }

        animationFrameRef.current = requestAnimationFrame(updateTimer);
      };

      animationFrameRef.current = requestAnimationFrame(updateTimer);

      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    } else if (!isRunning) {
      // Clean up animation frame when timer is paused or completed
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      lastTimestampRef.current = 0;
    }
  }, [isRunning, timeLeft, sessionType, playSessionTransition]);

  // Handle timer start
  const handleStart = () => {
    if (timeLeft > 0) {
      setIsRunning(true);
    }
  };

  // Handle timer pause
  const handlePause = () => {
    setIsRunning(false);
  };

  // Handle timer reset
  const handleReset = () => {
    setIsRunning(false);
    setSessionType('work');
    setTimeLeft(DEFAULT_WORK_DURATION);
    lastTimestampRef.current = 0;
  };

  // Check if timer is at initial state
  const isAtInitialState =
    timeLeft === DEFAULT_WORK_DURATION && sessionType === 'work';

  return (
    <TimerContainer>
      <SessionTypeIndicator sessionType={sessionType}>
        {sessionType === 'work' ? 'Work Session' : 'Break Time'}
      </SessionTypeIndicator>
      <TimeDisplay isActive={isRunning} sessionType={sessionType}>
        {formattedTime}
      </TimeDisplay>
      <TimerControls>
        {isRunning ? (
          <Button variant="warning" size="medium" onClick={handlePause}>
            Pause
          </Button>
        ) : (
          <Button
            variant="success"
            size="medium"
            onClick={handleStart}
            disabled={timeLeft === 0}
          >
            Start
          </Button>
        )}
        <Button
          variant="danger"
          size="medium"
          onClick={handleReset}
          disabled={isAtInitialState}
        >
          Reset
        </Button>
      </TimerControls>
    </TimerContainer>
  );
};

export default Timer;
