/**
 * Audio utility functions for the My Pomodoro application
 *
 * These functions handle audio playback for session transitions and other app events.
 */

// Augment the window interface to include the vendor-prefixed webkitAudioContext
declare global {
  interface Window {
    webkitAudioContext: typeof AudioContext;
  }
}

// --- Constants for Sound Properties ---
const BEEP_FREQUENCY_HZ = 440; // A4 note - a standard, pleasant beep
const BEEP_DURATION_S = 0.2;

const TRANSITION_NOTE_1_FREQ_HZ = 523.25; // C5 note
const TRANSITION_NOTE_2_FREQ_HZ = 659.25; // E5 note
const TRANSITION_NOTE_DURATION_S = 0.15;
const TRANSITION_NOTE_INTERVAL_S = 0.25;

// --- AudioContext Management ---

let audioContext: AudioContext | null = null;

/**
 * Lazily creates and returns a single AudioContext instance.
 * This is more efficient than creating a new context for each sound.
 * @returns The shared AudioContext, or null if the Web Audio API is not supported.
 */
const getAudioContext = (): AudioContext | null => {
  // Return null if not in a browser environment (e.g., SSR)
  if (typeof window === 'undefined') {
    return null;
  }

  // If context already exists, return it
  if (audioContext) {
    return audioContext;
  }

  // Try to create a new AudioContext
  try {
    const AudioContextClass = window.AudioContext || window.webkitAudioContext;
    if (AudioContextClass) {
      audioContext = new AudioContextClass();
    } else {
      console.warn('Web Audio API is not supported in this browser.');
    }
  } catch (error) {
    console.warn('Failed to create AudioContext:', error);
    // Set to null to prevent repeated attempts on failure
    audioContext = null;
  }

  return audioContext;
};

// --- Exported Playback Functions ---

/**
 * Play a beep sound using the Web Audio API.
 * @param volume - Volume level (0-100), defaults to 50.
 */
export const playBeep = (volume: number = 50): void => {
  const context = getAudioContext();
  if (!context) {
    return; // Silently fail if AudioContext is not available
  }

  try {
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);

    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(BEEP_FREQUENCY_HZ, context.currentTime);
    gainNode.gain.setValueAtTime(volume / 100, context.currentTime);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + BEEP_DURATION_S);
  } catch (error) {
    console.warn('Failed to play beep sound:', error);
  }
};

/**
 * Play a session transition sound (two short beeps).
 * @param volume - Volume level (0-100), defaults to 50.
 */
export const playSessionTransitionSound = (volume: number = 50): void => {
  const context = getAudioContext();
  if (!context) {
    return; // Silently fail if AudioContext is not available
  }

  try {
    // Helper to schedule a single note within the current AudioContext.
    const playNote = (time: number, frequency: number) => {
      const oscillator = context.createOscillator();
      const gainNode = context.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(context.destination);

      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(frequency, time);
      gainNode.gain.setValueAtTime(volume / 100, time);

      oscillator.start(time);
      oscillator.stop(time + TRANSITION_NOTE_DURATION_S);
    };

    const now = context.currentTime;
    playNote(now, TRANSITION_NOTE_1_FREQ_HZ);
    playNote(now + TRANSITION_NOTE_INTERVAL_S, TRANSITION_NOTE_2_FREQ_HZ);
  } catch (error) {
    console.warn('Failed to play session transition sound:', error);
  }
};
