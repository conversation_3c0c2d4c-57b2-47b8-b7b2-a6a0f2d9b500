# Dependencies
node_modules/
.npm/

# Build outputs
dist/
build/
.out/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Testing
coverage/
.nyc_output/

# Vercel
.vercel/

# Typescript
*.tsbuildinfo

# OS generated files
.DS_Store
Thumbs.db