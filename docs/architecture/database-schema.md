# Database Schema

Transforming the conceptual data models into concrete IndexedDB schemas for client-side storage:

## Sessions Store

```javascript
// IndexedDB Object Store Configuration
const sessionsStore = {
  name: "sessions",
  keyPath: "id",
  indexes: [
    {
      name: "by_date",
      keyPath: "startTime",
      options: { unique: false }
    },
    {
      name: "by_type",
      keyPath: "type",
      options: { unique: false }
    }
  ]
};

// Session Object Structure
{
  id: "string",           // Unique identifier (UUID)
  startTime: "Date",      // Session start timestamp
  endTime: "Date|null",   // Session end timestamp (null if ongoing)
  duration: "number",     // Duration in seconds (1500 for work, 300 for break)
  type: "string",         // "work" or "break"
  completed: "boolean"    // Whether session was completed
}
```

## Preferences Store

```javascript
// IndexedDB Object Store Configuration
const preferencesStore = {
  name: "preferences",
  keyPath: "userId"
};

// Preferences Object Structure
{
  userId: "string",        // User identifier (device ID for local storage)
  workDuration: "number",  // Work session duration in seconds (default: 1500)
  breakDuration: "number", // Break session duration in seconds (default: 300)
  audioEnabled: "boolean", // Whether audio alerts are enabled
  audioVolume: "number",   // Audio volume level (0-100)
  theme: "string"          // UI theme preference (optional)
}
```

## Database Schema Diagram

```mermaid
erDiagram
    SESSIONS ||--o{ SESSIONS : "related"
    PREFERENCES ||--|| PREFERENCES : "single"

    SESSIONS {
        string id PK
        Date startTime
        Date endTime
        number duration
        string type
        boolean completed
    }

    PREFERENCES {
        string userId PK
        number workDuration
        number breakDuration
        boolean audioEnabled
        number audioVolume
        string theme
    }