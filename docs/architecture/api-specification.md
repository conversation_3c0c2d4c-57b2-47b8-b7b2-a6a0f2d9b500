# API Specification

Based on the chosen REST API style from the Tech Stack, I'll create an OpenAPI 3.0 specification for the My Pomodoro application. Since this is a client-side PWA with local storage, the API will be minimal, focusing on any serverless functions needed for enhanced functionality.

## REST API Specification

```yaml
openapi: 3.0.0
info:
 title: My Pomodoro API
  version: 1.0.0
  description: API for the My Pomodoro application
servers:
 - url: https://api.mypomodoro.app
    description: Production server
  - url: http://localhost:3000/api
    description: Development server

paths:
 /sessions:
    get:
      summary: Get sessions
      description: Retrieve Pomodoro sessions
      parameters:
        - name: date
          in: query
          description: Date to filter sessions (YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
      responses:
        '200':
          description: A list of sessions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Session'
        '500':
          description: Internal server error

    post:
      summary: Create a session
      description: Record a completed Pomodoro session
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Session'
      responses:
        '201':
          description: Session created successfully
        '400':
          description: Invalid session data
        '500':
          description: Internal server error

  /sessions/{id}:
    get:
      summary: Get a session by ID
      description: Retrieve a specific Pomodoro session
      parameters:
        - name: id
          in: path
          description: Session ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Session data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
        '404':
          description: Session not found
        '500':
          description: Internal server error

  /preferences:
    get:
      summary: Get user preferences
      description: Retrieve current user preferences
      responses:
        '200':
          description: User preferences
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPreferences'
        '500':
          description: Internal server error

    put:
      summary: Update user preferences
      description: Update user preferences
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserPreferences'
      responses:
        '200':
          description: Preferences updated successfully
        '400':
          description: Invalid preferences data
        '500':
          description: Internal server error

components:
  schemas:
    Session:
      type: object
      properties:
        id:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
          nullable: true
        duration:
          type: number
        type:
          type: string
          enum: [work, break]
        completed:
          type: boolean
      required:
        - id
        - startTime
        - duration
        - type
        - completed

    UserPreferences:
      type: object
      properties:
        userId:
          type: string
        workDuration:
          type: number
        breakDuration:
          type: number
        audioEnabled:
          type: boolean
        audioVolume:
          type: number
          minimum: 0
          maximum: 100
        theme:
          type: string
      required:
        - userId
        - workDuration
        - breakDuration
        - audioEnabled
        - audioVolume