# Backend Architecture

Defining backend-specific architecture details for the My Pomodoro application. Based on our platform choice of Vercel, we'll be using serverless functions for any backend functionality needed, though the application primarily uses client-side storage.

## Service Architecture

Since the My Pomodoro application primarily uses client-side storage (IndexedDB) and doesn't require complex backend services, our backend architecture will be minimal. However, we'll define the structure for any serverless functions that might be needed for future enhancements.

### Serverless Architecture

#### Function Organization

```
api/
├── sessions/
│   ├── create.ts      # Create a new session record
│   ├── get.ts         # Get sessions (with optional date filtering)
│   └── getById.ts     # Get a specific session by ID
├── preferences/
│   ├── get.ts         # Get user preferences
│   └── update.ts      # Update user preferences
└── utils/
    ├── db.ts          # Database connection utilities
    └── validation.ts # Input validation utilities
```

#### Function Template

```typescript
// api/sessions/create.ts
import { VercelRequest, VercelResponse } from '@vercel/node';
import { v4 as uuidv4 } from 'uuid';
import { validateSession } from '../utils/validation';
import { saveSession } from '../utils/db';
import { Session } from '../../packages/shared/src/types';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed. Only POST requests are accepted.',
        timestamp: new Date().toISOString(),
        requestId: uuidv4()
      }
    });
  }

  try {
    // Rate limiting check
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    // In a real implementation, you would check against a rate limiting service here
    
    // Validate input
    const sessionData = req.body;
    const validationError = validateSession(sessionData);
    if (validationError) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid session data provided',
          details: validationError,
          timestamp: new Date().toISOString(),
          requestId: uuidv4()
        }
      });
    }

    // Add server-side fields
    const completeSessionData: Session = {
      ...sessionData,
      id: uuidv4(),
      startTime: new Date(sessionData.startTime),
      endTime: sessionData.endTime ? new Date(sessionData.endTime) : null
    };

    // Save session
    const savedSession = await saveSession(completeSessionData);
    
    // Log successful creation
    console.log(`Session created successfully: ${savedSession.id}`);
    
    // Return success response with proper headers
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'no-store');
    return res.status(201).json(savedSession);
  } catch (error: any) {
    console.error('Error creating session:', error);
    
    // Handle specific error types
    if (error.code === 'SQLITE_CONSTRAINT') {
      return res.status(409).json({
        error: {
          code: 'DUPLICATE_SESSION',
          message: 'Session already exists',
          timestamp: new Date().toISOString(),
          requestId: uuidv4()
        }
      });
    }
    
    // Return generic error response
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred while creating the session',
        timestamp: new Date().toISOString(),
        requestId: uuidv4()
      }
    });
  }
}
```

```typescript
// api/preferences/get.ts
import { VercelRequest, VercelResponse } from '@vercel/node';
import { getPreferences } from '../utils/db';
import { UserPreferences } from '../../packages/shared/src/types';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed. Only GET requests are accepted.',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      }
    });
  }

  try {
    // Extract user ID from query parameters or headers
    const userId = req.query.userId as string || req.headers['x-user-id'] as string;
    
    if (!userId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_USER_ID',
          message: 'User ID is required',
          timestamp: new Date().toISOString(),
          requestId: generateRequestId()
        }
      });
    }

    // Get preferences
    const preferences = await getPreferences(userId);
    
    if (!preferences) {
      // Return default preferences if none exist
      const defaultPreferences: UserPreferences = {
        userId,
        workDuration: 1500, // 25 minutes
        breakDuration: 300, // 5 minutes
        audioEnabled: true,
        audioVolume: 80
      };
      
      return res.status(200).json(defaultPreferences);
    }
    
    // Return success response
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'public, max-age=60'); // Cache for 1 minute
    return res.status(200).json(preferences);
  } catch (error: any) {
    console.error('Error getting preferences:', error);
    
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred while retrieving preferences',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      }
    });
  }
}

function generateRequestId(): string {
  return 'req-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}
```

```typescript
// api/utils/validation.ts
import { Session, UserPreferences } from '../../packages/shared/src/types';

interface ValidationError {
  field: string;
  issue: string;
}

export function validateSession(session: any): ValidationError | null {
  if (!session) {
    return { field: 'session', issue: 'Session data is required' };
  }
  
  if (!session.userId) {
    return { field: 'userId', issue: 'User ID is required' };
  }
  
  if (!session.startTime) {
    return { field: 'startTime', issue: 'Start time is required' };
  }
  
  if (typeof session.duration !== 'number' || session.duration <= 0) {
    return { field: 'duration', issue: 'Duration must be a positive number' };
  }
  
  if (session.type !== 'work' && session.type !== 'break') {
    return { field: 'type', issue: 'Type must be either "work" or "break"' };
  }
  
  if (typeof session.completed !== 'boolean') {
    return { field: 'completed', issue: 'Completed must be a boolean value' };
  }
  
  return null;
}

export function validatePreferences(preferences: any): ValidationError | null {
 if (!preferences) {
    return { field: 'preferences', issue: 'Preferences data is required' };
  }
  
  if (!preferences.userId) {
    return { field: 'userId', issue: 'User ID is required' };
  }
  
 if (typeof preferences.workDuration !== 'number' || preferences.workDuration <= 0) {
    return { field: 'workDuration', issue: 'Work duration must be a positive number' };
  }
  
  if (typeof preferences.breakDuration !== 'number' || preferences.breakDuration <= 0) {
    return { field: 'breakDuration', issue: 'Break duration must be a positive number' };
  }
  
  if (typeof preferences.audioEnabled !== 'boolean') {
    return { field: 'audioEnabled', issue: 'Audio enabled must be a boolean value' };
  }
  
  if (typeof preferences.audioVolume !== 'number' || preferences.audioVolume < 0 || preferences.audioVolume > 100) {
    return { field: 'audioVolume', issue: 'Audio volume must be a number between 0 and 100' };
  }
  
  return null;
}
```

## Database Architecture

Although the application primarily uses IndexedDB for client-side storage, if we were to implement server-side storage for features like synchronization across devices, we would use the following structure:

### Schema Design

```sql
-- Sessions table
CREATE TABLE sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration INTEGER NOT NULL,
  type VARCHAR(10) NOT NULL CHECK (type IN ('work', 'break')),
  completed BOOLEAN NOT NULL DEFAULT FALSE,
 created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_date ON sessions(start_time);
CREATE INDEX idx_sessions_type ON sessions(type);

-- Preferences table
CREATE TABLE preferences (
  user_id UUID PRIMARY KEY,
  work_duration INTEGER NOT NULL DEFAULT 1500,
  break_duration INTEGER NOT NULL DEFAULT 300,
  audio_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  audio_volume INTEGER NOT NULL DEFAULT 80 CHECK (audio_volume >= 0 AND audio_volume <= 100),
  theme VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Data Access Layer

```typescript
// utils/db.ts
import { Pool } from 'pg';
import { Session, UserPreferences } from '../../shared/src/types';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

export async function saveSession(session: Session): Promise<Session> {
  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO sessions (user_id, start_time, end_time, duration, type, completed)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;
    const values = [
      session.userId,
      session.startTime,
      session.endTime,
      session.duration,
      session.type,
      session.completed,
    ];
    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

export async function getSessions(userId: string, date?: Date): Promise<Session[]> {
 const client = await pool.connect();
  try {
    let query = 'SELECT * FROM sessions WHERE user_id = $1';
    const values = [userId];
    
    if (date) {
      query += ' AND DATE(start_time) = $2';
      values.push(date.toISOString().split('T')[0]);
    }
    
    query += ' ORDER BY start_time DESC';
    
    const result = await client.query(query, values);
    return result.rows;
  } finally {
    client.release();
  }
}

export async function savePreferences(preferences: UserPreferences): Promise<UserPreferences> {
  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO preferences (user_id, work_duration, break_duration, audio_enabled, audio_volume, theme)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (user_id) DO UPDATE
      SET work_duration = $2, break_duration = $3, audio_enabled = $4, audio_volume = $5, theme = $6, updated_at = NOW()
      RETURNING *
    `;
    const values = [
      preferences.userId,
      preferences.workDuration,
      preferences.breakDuration,
      preferences.audioEnabled,
      preferences.audioVolume,
      preferences.theme,
    ];
    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

export async function getPreferences(userId: string): Promise<UserPreferences | null> {
  const client = await pool.connect();
  try {
    const query = 'SELECT * FROM preferences WHERE user_id = $1';
    const result = await client.query(query, [userId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  } finally {
    client.release();
  }
}
```

## Authentication and Authorization

Since the current version of the application doesn't require user accounts or authentication, this section is minimal. However, if authentication were needed in the future, we would implement the following:

### Auth Flow

```mermaid
sequenceDiagram
    participant Client
    participant AuthAPI
    participant IdentityProvider
    participant Database

    Client->>AuthAPI: Request login
    AuthAPI->>IdentityProvider: Redirect to OAuth provider
    IdentityProvider->>Client: Redirect back with code
    Client->>AuthAPI: Send authorization code
    AuthAPI->>IdentityProvider: Exchange code for token
    IdentityProvider->>AuthAPI: Return access token
    AuthAPI->>Database: Store user session
    AuthAPI->>Client: Return JWT token
```

### Middleware/Guards

```typescript
// middleware/auth.ts
import { VercelRequest, VercelResponse, VercelApiHandler } from '@vercel/node';
import jwt from 'jsonwebtoken';

export function withAuth(handler: VercelApiHandler): VercelApiHandler {
  return async (req: VercelRequest, res: VercelResponse) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }
    
    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!);
      (req as any).user = decoded;
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }
  };
}

// Usage in API routes
import { withAuth } from '../middleware/auth';

export default withAuth(async function handler(req: VercelRequest, res: VercelResponse) {
  // Handler code here - user is authenticated
  const user = (req as any).user;
  // ... rest of the handler
});