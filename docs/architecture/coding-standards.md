# Coding Standards

Defining MINIMAL but CRITICAL standards for AI agents working on the My Pomodoro project. These focus only on project-specific rules that prevent common mistakes and will be used by dev agents.

## Critical Fullstack Rules

- **Type Sharing:** Always define types in packages/shared and import from there to ensure consistency between frontend and backend
- **API Calls:** Never make direct HTTP calls - use the service layer for all API interactions to maintain consistency and error handling
- **Environment Variables:** Access only through config objects, never process.env directly to ensure proper configuration management
- **Error Handling:** All API routes must use the standard error handler to maintain consistent error responses
- **State Updates:** Never mutate state directly - use proper state management patterns (React setState, useReducer, etc.) to ensure predictable state changes
- **Component Props:** Always define explicit prop types using TypeScript interfaces for better type safety and documentation
- **Database Access:** Use the data access layer for all database operations to maintain separation of concerns
- **Timer Management:** Use requestAnimationFrame for accurate timing in the timer component rather than setInterval to ensure smooth performance

## Naming Conventions

| Element | Frontend | Backend | Example |
|----------|---------|---------|
| Components | PascalCase | - | `UserProfile.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/user-profile` |
| Database Tables | - | snake_case | `user_profiles` |
| Functions | camelCase | camelCase | `calculateTimeLeft()` |
| Constants | UPPER_SNAKE_CASE | UPPER_SNAKE_CASE | `DEFAULT_WORK_DURATION` |
| Interfaces | PascalCase with 'I' prefix | PascalCase with 'I' prefix | `ISession`, `IUserPreferences` |
| Files | camelCase or PascalCase | kebab-case | `timerUtils.ts`, `user-profile.ts` |