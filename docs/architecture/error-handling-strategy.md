# Error Handling Strategy

Defining a unified error handling approach across frontend and backend for the My Pomodoro application.

## Error Flow

```mermaid
sequenceDiagram
    participant Client
    participant FrontendErrorHandler
    participant API
    participant BackendErrorHandler
    participant Database

    Client->>API: Request
    API->>Database: Database operation
    Database-->>API: Database error
    API->>BackendErrorHandler: Handle error
    BackendErrorHandler->>API: Format error response
    API-->>FrontendErrorHandler: Error response
    FrontendErrorHandler->>Client: Display user-friendly error
```

## Error Response Format

```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}

// Example error responses
const validationError: ApiError = {
  error: {
    code: "VALIDATION_ERROR",
    message: "Invalid input data",
    details: {
      field: "duration",
      issue: "Must be a positive number"
    },
    timestamp: "2025-08-29T10:00Z",
    requestId: "req-12345"
  }
};

const notFoundError: ApiError = {
  error: {
    code: "NOT_FOUND",
    message: "Session not found",
    timestamp: "2025-08-29T10:00:00Z",
    requestId: "req-12346"
  }
};

const internalError: ApiError = {
  error: {
    code: "INTERNAL_ERROR",
    message: "An unexpected error occurred",
    timestamp: "2025-08-29T10:00:00Z",
    requestId: "req-12347"
  }
};
```

## Frontend Error Handling

```typescript
// utils/errorHandler.ts
class FrontendErrorHandler {
  static handleApiError(error: any): string {
    if (error.response?.data?.error) {
      const apiError = error.response.data.error;
      
      // Handle specific error codes
      switch (apiError.code) {
        case 'VALIDATION_ERROR':
          return `Invalid input: ${apiError.message}`;
        case 'NOT_FOUND':
          return `Not found: ${apiError.message}`;
        case 'UNAUTHORIZED':
          // Redirect to login or show auth error
          window.location.href = '/login';
          return 'Please log in to continue';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    
    // Handle network errors
    if (error.request) {
      return 'Network error. Please check your connection and try again.';
    }
    
    // Handle other errors
    return 'An unexpected error occurred. Please try again.';
  }

  static displayError(message: string): void {
    // Display error in UI (could be a toast, modal, etc.)
    console.error(message);
    // Implementation would depend on the UI library used
 }
}

// services/apiClient.ts
import axios, { AxiosError } from 'axios';
import { FrontendErrorHandler } from '../utils/errorHandler';

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
});

// Add error interceptor
apiClient.interceptors.response.use(
  response => response,
  (error: AxiosError) => {
    const errorMessage = FrontendErrorHandler.handleApiError(error);
    FrontendErrorHandler.displayError(errorMessage);
    return Promise.reject(error);
  }
);

export default apiClient;
```

## Backend Error Handling

```typescript
// utils/errorHandler.ts
import { VercelRequest, VercelResponse } from '@vercel/node';

class BackendErrorHandler {
  static handleValidationError(res: VercelResponse, field: string, issue: string): void {
    res.status(400).json({
      error: {
        code: "VALIDATION_ERROR",
        message: "Invalid input data",
        details: {
          field,
          issue
        },
        timestamp: new Date().toISOString(),
        requestId: res.getHeader('X-Request-ID') as string || 'unknown'
      }
    });
  }

  static handleNotFoundError(res: VercelResponse, resource: string): void {
    res.status(404).json({
      error: {
        code: "NOT_FOUND",
        message: `${resource} not found`,
        timestamp: new Date().toISOString(),
        requestId: res.getHeader('X-Request-ID') as string || 'unknown'
      }
    });
  }

  static handleInternalServerError(res: VercelResponse, error: Error): void {
    console.error('Internal server error:', error);
    
    res.status(500).json({
      error: {
        code: "INTERNAL_ERROR",
        message: "An unexpected error occurred",
        timestamp: new Date().toISOString(),
        requestId: res.getHeader('X-Request-ID') as string || 'unknown'
      }
    });
  }
}

// Example usage in an API route
import { VercelRequest, VercelResponse } from '@vercel/node';
import { BackendErrorHandler } from '../utils/errorHandler';
import { validateSession } from '../utils/validation';
import { saveSession } from '../services/sessionService';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  try {
    // Add request ID for tracking
    res.setHeader('X-Request-ID', generateRequestId());
    
    // Validate input
    const sessionData = req.body;
    const validationError = validateSession(sessionData);
    if (validationError) {
      return BackendErrorHandler.handleValidationError(
        res, 
        validationError.field, 
        validationError.issue
      );
    }

    // Save session
    const savedSession = await saveSession(sessionData);
    
    // Return success response
    return res.status(201).json(savedSession);
 } catch (error: any) {
    // Handle specific errors
    if (error.code === 'NOT_FOUND') {
      return BackendErrorHandler.handleNotFoundError(res, 'Session');
    }
    
    // Handle all other errors as internal errors
    return BackendErrorHandler.handleInternalServerError(res, error);
  }
}

function generateRequestId(): string {
  return 'req-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}