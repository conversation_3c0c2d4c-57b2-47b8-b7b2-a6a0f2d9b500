# Frontend Architecture

Defining frontend-specific architecture details for the My Pomodoro application based on our chosen React framework and tech stack.

## Component Architecture

### Component Organization

```
src/
├── components/
│   ├── Timer/
│   │   ├── Timer.tsx
│   │   ├── Timer.styles.ts
│   │   └── index.ts
│   ├── SessionTracker/
│   │   ├── SessionTracker.tsx
│   │   ├── SessionTracker.styles.ts
│   │   └── index.ts
│   ├── Settings/
│   │   ├── Settings.tsx
│   │   ├── Settings.styles.ts
│   │   └── index.ts
│   ├── Audio/
│   │   ├── AudioManager.tsx
│   │   └── index.ts
│   └── common/
│       ├── Button/
│       │   ├── Button.tsx
│       │   ├── Button.styles.ts
│       │   └── index.ts
│       └── ProgressIndicator/
│           ├── ProgressIndicator.tsx
│           ├── ProgressIndicator.styles.ts
│           └── index.ts
├── hooks/
│   ├── useTimer.ts
│   ├── useSessions.ts
│   └── usePreferences.ts
├── services/
│   └── dataService.ts
├── contexts/
│   └── AppContext.tsx
├── styles/
│   ├── global.ts
│   └── theme.ts
└── utils/
    ├── timeUtils.ts
    └── audioUtils.ts
```

### Component Template

```typescript
// Timer/Timer.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTimer } from '../../hooks/useTimer';
import { Button } from '../common/Button';
import { ProgressIndicator } from '../common/ProgressIndicator';

interface TimerProps {
  workDuration: number;
  breakDuration: number;
  onSessionComplete: () => void;
}

export const Timer: React.FC<TimerProps> = ({ 
  workDuration, 
  breakDuration, 
  onSessionComplete 
}) => {
  const {
    timeLeft,
    isRunning,
    sessionType,
    startTimer,
    pauseTimer,
    resetTimer
  } = useTimer(workDuration, breakDuration, onSessionComplete);

  return (
    <TimerContainer>
      <SessionTypeIndicator sessionType={sessionType}>
        {sessionType === 'work' ? 'Work Session' : 'Break Time'}
      </SessionTypeIndicator>
      <TimeDisplay>{formatTime(timeLeft)}</TimeDisplay>
      <ProgressIndicator 
        progress={calculateProgress(timeLeft, sessionType === 'work' ? workDuration : breakDuration)} 
      />
      <ButtonContainer>
        {!isRunning ? (
          <Button onClick={startTimer}>Start</Button>
        ) : (
          <Button onClick={pauseTimer}>Pause</Button>
        )}
        <Button onClick={resetTimer}>Reset</Button>
      </ButtonContainer>
    </TimerContainer>
  );
};

// Styled components would be in Timer.styles.ts
const TimerContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
`;

const SessionTypeIndicator = styled.div<{ sessionType: 'work' | 'break' }>`
 font-size: 1.5rem;
  font-weight: bold;
  color: ${props => props.sessionType === 'work' ? '#2E7D32' : '#01579B'};
  margin-bottom: 1rem;
`;

const TimeDisplay = styled.div`
  font-size: 4rem;
  font-family: 'Roboto Mono', monospace;
  margin: 1rem 0;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
`;
```

## State Management Architecture

### State Structure

```typescript
// Application state structure
interface AppState {
  timer: {
    timeLeft: number;
    isRunning: boolean;
    sessionType: 'work' | 'break';
    currentSessionId: string | null;
  };
  sessions: {
    today: Session[];
    history: Session[];
  };
  preferences: UserPreferences;
}

// Timer-specific state
interface TimerState {
  timeLeft: number;
  isRunning: boolean;
  sessionType: 'work' | 'break';
  currentSessionId: string | null;
}
```

### State Management Patterns

- **Context API with useReducer**: For global application state management
- **Custom Hooks**: For encapsulating timer logic and data fetching
- **Local Component State**: For UI-specific state that doesn't need to be shared
- **State Persistence**: Preferences and session data persisted to IndexedDB

## Routing Architecture

### Route Organization

```
src/
└── pages/
    ├── Home/
    │   ├── Home.tsx
    │   └── index.ts
    ├── Statistics/
    │   ├── Statistics.tsx
    │   └── index.ts
    └── Settings/
        ├── Settings.tsx
        └── index.ts
```

### Protected Route Pattern

```typescript
// Example of a simple route setup
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Home } from '../pages/Home';
import { Statistics } from '../pages/Statistics';
import { Settings } from '../pages/Settings';

export const AppRoutes: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/statistics" element={<Statistics />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Router>
  );
};
```

## Frontend Services Layer

### API Client Setup

```typescript
// services/dataService.ts
class DataService {
  private dbName = 'MyPomodoro';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create sessions store
        const sessionsStore = db.createObjectStore('sessions', { keyPath: 'id' });
        sessionsStore.createIndex('by_date', 'startTime', { unique: false });
        sessionsStore.createIndex('by_type', 'type', { unique: false });
        
        // Create preferences store
        db.createObjectStore('preferences', { keyPath: 'userId' });
      };
    });
  }

  // Session methods
  async saveSession(session: Session): Promise<void> {
    if (!this.db) await this.init();
    const transaction = this.db!.transaction(['sessions'], 'readwrite');
    const store = transaction.objectStore('sessions');
    await store.put(session);
  }

  async getSessions(date?: Date): Promise<Session[]> {
    if (!this.db) await this.init();
    const transaction = this.db!.transaction(['sessions'], 'readonly');
    const store = transaction.objectStore('sessions');
    
    if (date) {
      // Filter by date
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0);
      const endDate = new Date(date);
      endDate.setHours(23, 59, 999);
      
      const index = store.index('by_date');
      const request = index.getAll(IDBKeyRange.bound(startDate, endDate));
      return new Promise((resolve) => {
        request.onsuccess = () => resolve(request.result);
      });
    } else {
      // Get all sessions
      const request = store.getAll();
      return new Promise((resolve) => {
        request.onsuccess = () => resolve(request.result);
      });
    }
  }

  // Preferences methods
  async savePreferences(preferences: UserPreferences): Promise<void> {
    if (!this.db) await this.init();
    const transaction = this.db!.transaction(['preferences'], 'readwrite');
    const store = transaction.objectStore('preferences');
    await store.put(preferences);
  }

  async getPreferences(userId: string): Promise<UserPreferences | undefined> {
    if (!this.db) await this.init();
    const transaction = this.db!.transaction(['preferences'], 'readonly');
    const store = transaction.objectStore('preferences');
    const request = store.get(userId);
    
    return new Promise((resolve) => {
      request.onsuccess = () => resolve(request.result);
    });
  }
}

export const dataService = new DataService();
```

### Service Example

```typescript
// hooks/useSessions.ts
import { useState, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { Session } from '../types';

export const useSessions = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      const today = new Date();
      const todaySessions = await dataService.getSessions(today);
      setSessions(todaySessions);
    } catch (error) {
      console.error('Error loading sessions:', error);
    } finally {
      setLoading(false);
    }
 };

  const recordSession = async (session: Session) => {
    try {
      await dataService.saveSession(session);
      setSessions(prev => [...prev, session]);
    } catch (error) {
      console.error('Error recording session:', error);
    }
  };

  return { sessions, loading, recordSession, loadSessions };
};