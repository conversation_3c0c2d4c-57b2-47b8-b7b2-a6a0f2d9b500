# Development Workflow

Defining the development setup and workflow for the My Pomodoro fullstack application.

## Local Development Setup

### Prerequisites

```bash
# Install Node.js (version 18 or higher)
# Install npm (comes with Node.js) or yarn
# Install Git
# Install VS Code (recommended editor)
# Install Vercel CLI globally
npm install -g vercel
```

### Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd my-pomodoro

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with appropriate values

# Initialize Vercel project (if not already set up)
vercel login
vercel link

# Start development servers
npm run dev
```

### Development Commands

```bash
# Start all services (frontend and backend)
npm run dev

# Start frontend only
npm run dev:web

# Start backend only
npm run dev:api

# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run end-to-end tests
npm run test:e2e

# Build for production
npm run build

# Lint code
npm run lint

# Format code
npm run format

# Update dependencies
npm run update:deps

# Audit dependencies for security vulnerabilities
npm run audit:deps

# Clean install (removes node_modules and reinstalls)
npm run clean:install
```

## Dependency Management Strategy

### Dependency Updates:
- Use `npm outdated` to check for outdated dependencies weekly
- Automate dependency updates with Dependabot or Renovate bot
- Set up a monthly dependency review process to evaluate major version updates
- Pin dependency versions in `package-lock.json` for reproducible builds

### Security Management:
- Run `npm audit` weekly to check for known vulnerabilities
- Use `npm audit fix` to automatically fix vulnerabilities when possible
- Set up automated security scanning in CI/CD pipeline
- Establish a process for addressing high and critical severity vulnerabilities within 24 hours

### Dependency Selection:
- Prefer well-maintained, popular libraries with active communities
- Check download statistics and GitHub activity before adding new dependencies
- Avoid dependencies with known security issues or infrequent updates
- Use bundlephobia.com to check the size impact of new dependencies

### Monorepo Dependency Management:
- Use npm workspaces to manage dependencies across packages
- Share common dependencies in the root `package.json` when appropriate
- Use `npm ls` to visualize dependency tree and identify duplicates
- Implement a consistent versioning strategy across all packages

### Example Scripts for Dependency Management:

```json
{
  "scripts": {
    "update:deps": "npm outdated && npm update",
    "audit:deps": "npm audit --audit-level=moderate",
    "clean:install": "rm -rf node_modules package-lock.json && npm install",
    "check:deps": "npm ls --depth=0"
  }
}
```

## Environment Configuration

### Required Environment Variables

```bash
# Frontend (.env.local)
REACT_APP_API_URL=http://localhost:3001
REACT_APP_ENV=development

# Backend (.env)
DATABASE_URL=postgresql://user:password@localhost:5432/mypomodoro
JWT_SECRET=your-jwt-secret-key
VERCEL_ENV=development

# Shared
NODE_ENV=development