# Core Workflows

Here are the key system workflows illustrated using sequence diagrams, showing component interactions for critical user journeys from the PRD:

## Starting a Pomodoro Session

```mermaid
sequenceDiagram
    participant User
    participant TimerComponent
    participant AudioManager
    participant StateManager
    participant DataAccessLayer
    participant IndexedDB

    User->>TimerComponent: Click Start Button
    TimerComponent->>StateManager: Set timer state to running
    TimerComponent->>AudioManager: Prepare audio (if enabled)
    TimerComponent->>DataAccessLayer: Create new session record
    DataAccessLayer->>IndexedDB: Save session with start time
    TimerComponent->>User: Display running timer
```

## Completing a Work Session and Starting a Break

```mermaid
sequenceDiagram
    participant TimerComponent
    participant AudioManager
    participant StateManager
    participant DataAccessLayer
    participant IndexedDB

    TimerComponent->>TimerComponent: Timer reaches 00:00
    TimerComponent->>StateManager: Set timer state to completed
    TimerComponent->>AudioManager: Play session complete sound
    TimerComponent->>DataAccessLayer: Update session with end time
    DataAccessLayer->>IndexedDB: Save updated session
    TimerComponent->>StateManager: Switch to break mode
    TimerComponent->>TimerComponent: Start break timer automatically
```

## Customizing Timer Settings

```mermaid
sequenceDiagram
    participant User
    participant SettingsComponent
    participant StateManager
    participant DataAccessLayer
    participant IndexedDB

    User->>SettingsComponent: Update work duration to 30 min
    SettingsComponent->>StateManager: Update preferences in state
    SettingsComponent->>DataAccessLayer: Save updated preferences
    DataAccessLayer->>IndexedDB: Store new preferences
    SettingsComponent->>User: Confirm settings saved
```

## Viewing Daily Session Statistics

```mermaid
sequenceDiagram
    participant User
    participant SessionTrackerComponent
    participant DataAccessLayer
    participant IndexedDB

    User->>SessionTrackerComponent: Navigate to statistics view
    SessionTrackerComponent->>DataAccessLayer: Request today's sessions
    DataAccessLayer->>IndexedDB: Query sessions for current date
    IndexedDB->>DataAccessLayer: Return session data
    DataAccessLayer->>SessionTrackerComponent: Provide session data
    SessionTrackerComponent->>User: Display session count and history