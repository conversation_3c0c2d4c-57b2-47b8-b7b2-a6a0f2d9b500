# Data Models

Based on the PRD requirements, the My Pomodoro application requires data models for tracking Pomodoro sessions and user preferences. The application needs to store session data locally using IndexedDB and maintain user preferences across sessions.

## Session

**Purpose:** Represents a single Pomodoro session, either work or break, with timing information and completion status.

**Key Attributes:**
- id: string - Unique identifier for the session
- startTime: Date - When the session started
- endTime: Date - When the session ended (null if ongoing)
- duration: number - Duration in seconds (1500 for work, 300 for break as default)
- type: "work" | "break" - Session type
- completed: boolean - Whether the session was completed successfully

**TypeScript Interface:**
```typescript
interface Session {
  id: string;
  startTime: Date;
  endTime: Date | null;
  duration: number;
  type: "work" | "break";
  completed: boolean;
}
```

**Relationships:**
- None (standalone entity)

## UserPreferences

**Purpose:** Stores user-customizable settings for the Pomodoro application.

**Key Attributes:**
- userId: string - Unique identifier for the user (could be device ID for local storage)
- workDuration: number - Work session duration in seconds (default: 1500)
- breakDuration: number - Break session duration in seconds (default: 300)
- audioEnabled: boolean - Whether audio alerts are enabled
- audioVolume: number - Audio volume level (0-100)
- theme: string - UI theme preference (optional)

**TypeScript Interface:**
```typescript
interface UserPreferences {
  userId: string;
  workDuration: number;
  breakDuration: number;
  audioEnabled: boolean;
  audioVolume: number;
  theme?: string;
}
```

**Relationships:**
- None (standalone entity with single instance per device/user)