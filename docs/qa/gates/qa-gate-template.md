# QA Gate Decision Template

## Story Reference
- Epic: [EPIC-NUMBER]
- Story: [STORY-NUMBER] - [STORY TITLE]

## Gate Decision
**Status**: [PASS|CONCERNS|FAIL|WAIVED]

## Decision Date
[YYYY-MM-DD]

## QA Analyst
[ANALYST NAME]

## Rationale
[Detailed explanation of the decision including what was evaluated and why this decision was made]

## Evidence
### Test Results
- Unit Tests: [Pass/Fail - Coverage %]
- Integration Tests: [Pass/Fail - Coverage %]
- E2E Tests: [Pass/Fail - Coverage %]
- Manual Testing: [Pass/Fail - Number of test cases]

### Defects Found
| ID | Severity | Status | Description |
|----|----------|--------|-------------|
| [ID] | [High/Medium/Low] | [Open/Closed] | [Description] |

### Non-Functional Requirements Validation
- Performance: [Compliant/Non-Compliant]
- Security: [Compliant/Non-Compliant]
- Accessibility: [Compliant/Non-Compliant]
- Usability: [Compliant/Non-Compliant]

## Required Actions
[If status is CONCERNS or FAIL, list specific actions that must be completed before the story can pass QA]

1. [Action 1]
2. [Action 2]
3. [Action 3]

## Recommendations
[Any additional recommendations for improvement, even if the story passes]

## Risk Assessment
- **Risk Level**: [Low/Medium/High]
- **Risk Description**: [Description of potential risks if story is deployed as is]
- **Mitigation**: [How risks are mitigated]

## Next Steps
[What happens next based on this decision]