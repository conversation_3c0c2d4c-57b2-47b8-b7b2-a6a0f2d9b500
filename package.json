{"name": "my-pomodoro", "version": "1.0.0", "description": "A Pomodoro timer application built with React, TypeScript, and styled-components", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace=web", "build": "npm run build --workspace=web", "test": "npm run test --workspace=web", "lint": "npm run lint --workspace=web", "format": "npm run format --workspace=web", "prepare": "husky"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "prettier": "^3.0.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,md}": ["prettier --write"]}}